import { Entity, EntityComponentTypes } from "@minecraft/server";
import { selectAttack } from "./controller";
import { stopPiglinChampionSounds, ATTACK_SOUND_MAP } from "./soundManager";
import { getTarget } from "../general_mechanics/targetUtils";
import { executeAttack } from "./attackExecutor";

/**
 * Handles the piglin champion boss mechanics
 * @param piglinChampion The piglin champion entity
 */
export function piglinChampionMechanics(piglinChampion: Entity): void {
  // Skip if entity is not valid
  try {
    if (!piglinChampion) return;
  } catch (e) {
    return;
  }
  const attack = piglinChampion.getProperty("ptd_dbb:attack") as string;
  const stunStandingTriggered = piglinChampion.getProperty("ptd_dbb:stun_standing_triggered") as boolean;
  const stunSittingTriggered = piglinChampion.getProperty("ptd_dbb:stun_sitting_triggered") as boolean;

  // Target detection and combat mechanics
  const target = getTarget(piglinChampion, piglinChampion.location, 32, ["piglin_champion"]);

  // Get attack properties
  // We already got the attack property earlier for death check
  const coolingDown = piglinChampion.getProperty("ptd_dbb:cooling_down") as boolean;

  if (target) {
    // Check health for healing ability and stun mechanics
    const healthComponent = piglinChampion.getComponent(EntityComponentTypes.Health);
    const health = healthComponent?.currentValue || 0;
    const maxHealth = healthComponent?.defaultValue || 0; // Default max health if not set
    const lastHealThreshold = piglinChampion.getProperty("ptd_dbb:last_heal_threshold") as number;

    // Calculate current health percentage threshold (0-3 for 75%, 50%, 25%, 0%)
    const currentThreshold = Math.floor((health / maxHealth) * 4);

    // Calculate health percentage for stun mechanics
    const healthPercentage = (health / maxHealth) * 100;

    // Check for stun standing at 65% health
    if (healthPercentage <= 65 && !stunStandingTriggered && attack !== "stunned_standing" && attack !== "stunned_sitting" && !isDead) {
      // Cancel any ongoing attack
      if (attack !== "none") {
        // Stop all sounds when resetting attack
        stopPiglinChampionSounds(piglinChampion);

        piglinChampion.triggerEvent("ptd_dbb:reset_attack");
      }

      // Stop all other sound effects except for stunned standing sound
      const stunSound = ATTACK_SOUND_MAP["stunned_standing"];
      stopPiglinChampionSounds(piglinChampion, stunSound);

      // Trigger stunned standing
      piglinChampion.triggerEvent("ptd_dbb:stunned_standing");

      // Apply slowness effect for the duration of the stun
      piglinChampion.addEffect("minecraft:slowness", 240, { amplifier: 250, showParticles: false });

      return; // Exit early to prevent other actions this tick
    }

    // Check for stun sitting at 35% health
    if (healthPercentage <= 35 && !stunSittingTriggered && attack !== "stunned_sitting" && !isDead) {
      // Cancel any ongoing attack
      if (attack !== "none") {
        // Stop all sounds when resetting attack
        stopPiglinChampionSounds(piglinChampion);

        piglinChampion.triggerEvent("ptd_dbb:reset_attack");
      }

      // Stop all other sound effects except for stunned sitting sound
      const stunSound = ATTACK_SOUND_MAP["stunned_sitting"];
      stopPiglinChampionSounds(piglinChampion, stunSound);

      // Trigger stunned sitting
      piglinChampion.triggerEvent("ptd_dbb:stunned_sitting");

      // Apply slowness effect for the duration of the stun
      piglinChampion.addEffect("minecraft:slowness", 333, { amplifier: 250, showParticles: false });

      return; // Exit early to prevent other actions this tick
    }

    // Handle attack execution if already in an attack
    if (attack !== "none") {
      // Check if this is a new attack that needs to be executed
      // We need to track if the attack has been started to avoid re-executing
      const attackStarted = piglinChampion.getProperty("ptd_dbb:attack_started") as boolean;

      if (!attackStarted) {
        // Mark attack as started and execute it
        piglinChampion.setProperty("ptd_dbb:attack_started", true);
        executeAttack(piglinChampion, attack);
      }
    }
    // Select a new attack if not currently attacking and not cooling down
    else if (!coolingDown) {
      // Check if we need to heal first
      if (currentThreshold < lastHealThreshold) {
        // Update last heal threshold
        piglinChampion.setProperty("ptd_dbb:last_heal_threshold", currentThreshold);

        // Stop all other sound effects except for healing sound
        const healingSound = ATTACK_SOUND_MAP["healing"];
        stopPiglinChampionSounds(piglinChampion, healingSound);

        // Trigger healing ability
        piglinChampion.triggerEvent("ptd_dbb:healing_ability");

        // Apply slowness with amplifier 250 for the duration of the healing animation
        piglinChampion.addEffect("minecraft:slowness", 158, { amplifier: 250, showParticles: false });
      } else {
        // Otherwise select a normal attack
        selectAttack(piglinChampion, target);
      }
    }
  }
  // Reset attack if no target
  else if (attack !== "none") {
    // Stop all sounds when resetting attack
    stopPiglinChampionSounds(piglinChampion);

    piglinChampion.triggerEvent("ptd_dbb:reset_attack");
  }
}
